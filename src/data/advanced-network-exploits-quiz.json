{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-network-exploits", "title": "Advanced Network Exploitation Techniques", "description": "Expert-level quiz covering advanced network protocol attacks, traffic analysis, and real-world network exploitation techniques.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T14:45:57Z", "tags": ["network-security", "protocol-attacks", "mitm", "traffic-analysis", "network-exploitation"], "passing_score_percentage": 80, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "dns_rebinding_attack", "type": "multiple_choice", "difficulty": "advanced", "text": "You're testing a web application that includes a feature to fetch content from URLs provided by authenticated users. The application includes the following security checks:\n\n1. URLs must begin with `http://` or `https://`\n2. URLs cannot point to internal IP addresses (10.0.0.0/8, **********/12, ***********/16, *********/8)\n3. URLs must resolve to a public IP address when the request is initiated\n\nDespite these protections, you believe you can still access internal services. Which attack technique would most likely bypass these restrictions?", "points": 3, "single_correct_answer": true, "options": [{"id": "dns_opt1", "text": "DNS Rebinding - Use a domain that initially resolves to a public IP but changes to an internal IP after the security check", "is_correct": true, "feedback": "Correct! DNS rebinding exploits the time gap between DNS resolution during security checks and the actual connection establishment, allowing access to internal resources."}, {"id": "dns_opt2", "text": "SSRF via redirect - Use a public server that redirects to an internal IP address", "is_correct": false, "feedback": "While redirects can be used for SSRF, the application would likely check the redirect destination against the same IP blacklist, preventing access to internal IPs."}, {"id": "dns_opt3", "text": "Using IPv6 addresses to bypass IPv4 filtering", "is_correct": false, "feedback": "While IPv6 bypass can work in some cases, the scenario specifically mentions the application checks for 'internal IP addresses', which would likely include IPv6 equivalents."}, {"id": "dns_opt4", "text": "DNS Cache Poisoning - Corrupt the DNS cache to make internal hostnames resolve to public IPs", "is_correct": false, "feedback": "DNS cache poisoning attacks target DNS resolvers to return incorrect information, but this wouldn't help bypass the application's security checks in the described scenario."}], "feedback_correct": "Excellent! DNS rebinding attacks exploit the time-of-check vs. time-of-use (TOCTOU) gap between when a domain is resolved for security checks and when it's actually used for the connection.", "feedback_incorrect": "The key vulnerability is the time gap between when domain resolution occurs for security checking and when the actual connection is made. DNS rebinding exploits this by changing DNS responses between these two operations.", "explanation": "DNS rebinding is a sophisticated attack that exploits the time gap between security checks and connection establishment. In this scenario, the application validates that a URL resolves to a public IP address when the security check is performed, but doesn't re-validate when making the actual request. To exploit this, an attacker can use a domain they control with a very short TTL (Time-to-Live) value, initially configuring it to resolve to a public IP. After the security check passes, the attacker quickly changes the DNS record to point to an internal IP address. When the application makes the actual request, it connects to the internal IP, bypassing the security check. This technique can be used to access internal services, perform port scanning, or exploit vulnerable internal applications. To prevent DNS rebinding attacks, applications should either re-validate DNS resolutions before making connections, use a whitelist approach instead of blacklisting, or implement proper network segmentation."}, {"question_id": "http_request_smuggling", "type": "multiple_choice", "difficulty": "expert", "text": "During a penetration test, you discover a web application behind a reverse proxy setup (Front-end proxy -> Back-end server). You send the following HTTP request:\n\n```http\nPOST / HTTP/1.1\nHost: vulnerable-site.com\nContent-Length: 5\nTransfer-Encoding: chunked\n\n0\r\n\r\nX\n```\n\nAfter sending this request, you notice that part of your next request appears to be processed differently than expected. What vulnerability are you likely exploiting, and why does this occur?", "points": 3, "single_correct_answer": true, "options": [{"id": "smuggle_opt1", "text": "HTTP Request Smuggling (CL.TE) - The front-end server uses Content-Length while the back-end uses Transfer-Encoding", "is_correct": true, "feedback": "Correct! This is a CL.TE request smuggling vulnerability where the front-end server uses Content-Length: 5 (seeing the body as '0\\r\\n\\r\\nX') while the back-end uses Transfer-Encoding: chunked (interpreting '0\\r\\n\\r\\n' as the end of the request, with 'X' becoming part of the next request)."}, {"id": "smuggle_opt2", "text": "HTTP Response Splitting - The server reflects your input in the response headers", "is_correct": false, "feedback": "HTTP Response Splitting involves injecting CRLF characters to split a response into multiple responses, which is different from what's happening in this scenario."}, {"id": "smuggle_opt3", "text": "HTTP Parameter Pollution - Multiple parameters with the same name are handled differently", "is_correct": false, "feedback": "HTTP Parameter Pollution involves confusion when handling duplicate parameter names, which isn't relevant to this scenario involving HTTP message parsing."}, {"id": "smuggle_opt4", "text": "HTTP Desync Attack (TE.CL) - The front-end server uses Transfer-Encoding while the back-end uses Content-Length", "is_correct": false, "feedback": "This describes a TE.CL variant of request smuggling, which is the opposite of what's happening in this scenario where the front-end uses Content-Length and the back-end uses Transfer-Encoding."}], "feedback_correct": "Excellent! You correctly identified a CL.TE HTTP Request Smuggling vulnerability, which occurs when the front-end server uses Content-Length while the back-end server uses Transfer-Encoding to determine message boundaries.", "feedback_incorrect": "This is a classic example of HTTP Request Smuggling, specifically the CL.TE variant where the front-end proxy and back-end server disagree on how to interpret request boundaries.", "explanation": "HTTP Request Smuggling exploits inconsistencies in how front-end and back-end servers parse HTTP requests. In this specific case, you're exploiting a CL.TE (Content-Length vs. Transfer-Encoding) vulnerability. The front-end server prioritizes the Content-Length header, interpreting the request body as 5 bytes long ('0\\r\\n\\r\\nX'). However, the back-end server prioritizes the Transfer-Encoding: chunked header, interpreting '0\\r\\n\\r\\n' as an empty chunk that ends the request, treating the 'X' as the beginning of the next request. This desynchronization allows you to 'smuggle' parts of one request into another, which can lead to cache poisoning, request queue poisoning, or bypassing security controls. This vulnerability can be particularly dangerous in shared hosting environments or applications that process user requests in sequence. Modern web servers and proxies have improved their handling of ambiguous requests, but inconsistencies still exist, especially in complex architectures with multiple layers of proxies and servers."}, {"question_id": "bgp_hijacking", "type": "multiple_choice", "difficulty": "advanced", "text": "You observe the following anomalous behavior on the internet:\n\n1. Traffic destined for a major financial institution (AS12345) is suddenly routing through an unusual autonomous system (AS67890) in another country\n2. The routing change occurred after AS67890 began advertising more specific prefixes for IP ranges belonging to the financial institution\n3. After 30 minutes, routing returned to normal\n\nWhat type of network attack does this pattern suggest?", "points": 2, "single_correct_answer": true, "options": [{"id": "bgp_opt1", "text": "BGP Hijacking - Advertising unauthorized or more specific routes to redirect traffic", "is_correct": true, "feedback": "Correct! This is a classic BGP hijacking attack where an autonomous system announces more specific (therefore preferred) routes for IP space it doesn't legitimately own."}, {"id": "bgp_opt2", "text": "DNS Cache Poisoning - Corrupting DNS resolvers to return incorrect IP addresses", "is_correct": false, "feedback": "DNS cache poisoning would affect name resolution, not the routing of traffic to already-resolved IP addresses, and wouldn't involve autonomous systems or BGP prefixes."}, {"id": "bgp_opt3", "text": "ARP Spoofing - Sending fake ARP messages to associate an attacker's MAC address with a legitimate IP", "is_correct": false, "feedback": "ARP spoofing is a local network attack that operates at layer 2, not a global routing attack involving autonomous systems and BGP."}, {"id": "bgp_opt4", "text": "Domain Hijacking - Unauthorized changes to domain registration information", "is_correct": false, "feedback": "Domain hijacking involves changing the control of a domain name at the registrar level, not manipulating network routes for IP prefixes."}], "feedback_correct": "Excellent! BGP hijacking exploits the trust-based nature of the Border Gateway Protocol, allowing an attacker to temporarily redirect internet traffic by advertising more specific or unauthorized routes.", "feedback_incorrect": "This scenario describes BGP hijacking, where an autonomous system advertises unauthorized routes for IP prefixes it doesn't legitimately own, causing internet traffic to be misdirected through networks controlled by the attacker.", "explanation": "BGP (Border Gateway Protocol) hijacking is a sophisticated attack against the internet's routing infrastructure. BGP relies largely on trust between autonomous systems (ISPs and large networks) to advertise legitimate routes for their IP space. In a BGP hijacking attack, a malicious or compromised autonomous system advertises routes for IP prefixes it doesn't legitimately own. Since BGP prefers more specific routes (longer prefix matches), advertising more specific prefixes can override legitimate routes even when the legitimate routes are still being advertised. When successful, this attack reroutes internet traffic through the attacker's network, allowing for traffic interception (man-in-the-middle), network traffic analysis, or in some cases, denial of service. BGP hijacks can be difficult to detect and prevent due to the distributed, trust-based nature of internet routing. Countermeasures include BGP security extensions like RPKI (Resource Public Key Infrastructure), which cryptographically validates route advertisements, and monitoring services that detect suspicious routing changes."}]}}