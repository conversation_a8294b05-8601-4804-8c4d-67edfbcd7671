{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "crypto-attacks-advanced", "title": "Advanced Cryptographic Attacks", "description": "Real-world cryptographic vulnerabilities including padding oracle attacks, timing attacks, and practical cryptographic implementation flaws.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T14:45:57Z", "tags": ["cryptography", "crypto-attacks", "padding-oracle", "side-channel", "implementation-flaws"], "passing_score_percentage": 75, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "padding_oracle_attack", "type": "multiple_choice", "difficulty": "advanced", "text": "During a security assessment, you discover a web application that encrypts sensitive data using AES-CBC and sends it to clients as a hex-encoded string. When clients submit this data back, the server decrypts it and responds with one of the following messages:\n\n- \"Data processed successfully\" (when decryption succeeds)\n- \"Invalid data format\" (when padding is incorrect)\n- \"Data tampering detected\" (when MAC verification fails)\n\nWhat vulnerability is likely present, and how would you exploit it?", "points": 3, "single_correct_answer": true, "options": [{"id": "padding_opt1", "text": "Padding Oracle vulnerability - Modify ciphertext blocks and observe error messages to iteratively decrypt the data without knowing the key", "is_correct": true, "feedback": "Correct! This is a classic padding oracle vulnerability where error messages leak information about padding validity, allowing an attacker to decrypt the ciphertext without knowing the key."}, {"id": "padding_opt2", "text": "MAC bypass - Remove the MAC from the encrypted data to bypass integrity checks", "is_correct": false, "feedback": "This approach would likely trigger the 'Data tampering detected' error and wouldn't allow you to decrypt the data."}, {"id": "padding_opt3", "text": "Key recovery attack - Analyze response times to determine key bits", "is_correct": false, "feedback": "This describes a timing attack, which isn't applicable based on the information provided. The error messages, not timing differences, are leaking information in this scenario."}, {"id": "padding_opt4", "text": "Known plaintext attack - Use known plaintext patterns to directly recover the encryption key", "is_correct": false, "feedback": "AES is resistant to known plaintext attacks, and this scenario doesn't provide the conditions needed for such an attack."}], "feedback_correct": "Excellent! You correctly identified the padding oracle vulnerability, a practical cryptographic attack that exploits error messages about padding to decrypt ciphertext without knowing the key.", "feedback_incorrect": "This scenario describes a padding oracle vulnerability. When a system provides different error messages for padding errors versus other errors, attackers can manipulate ciphertexts to iteratively decrypt data without knowing the key.", "explanation": "A padding oracle attack exploits information leakage from error messages related to padding validation during decryption. In CBC mode, each plaintext block is XORed with the previous ciphertext block before encryption. This means if you can manipulate a ciphertext block and observe whether the resulting padding is valid, you can deduce information about the plaintext. The attack works by systematically modifying bytes in a ciphertext block and observing the server's responses. When the padding is correct, you get a different error (or success message) than when it's incorrect. Through this process, you can determine the plaintext one byte at a time without knowing the encryption key. This is why cryptographic systems should use authenticated encryption (like AES-GCM) and provide uniform error messages that don't leak information about padding validity. Importantly, the system should validate the MAC before checking padding to prevent these attacks."}, {"question_id": "weak_rsa_implementation", "type": "multiple_choice", "difficulty": "expert", "text": "You're testing a system that uses RSA encryption with the following parameters:\n\n```\nPublic key (e, n): (3, 9551367822787863459)\n```\n\nThe system encrypts a short message m using the formula c = m^e mod n, where c is the ciphertext. You intercept a ciphertext: c = 5926304321782421243\n\nWhich of the following vulnerabilities is likely present, and how would you exploit it?", "points": 3, "single_correct_answer": true, "options": [{"id": "rsa_opt1", "text": "Common Modulus Attack - Obtain another ciphertext with a different public exponent and use the extended Euclidean algorithm", "is_correct": false, "feedback": "A common modulus attack requires the same message encrypted with two different exponents, which isn't the scenario described."}, {"id": "rsa_opt2", "text": "Low Public Exponent with No Padding - Compute the cube root of the ciphertext directly without modular reduction", "is_correct": true, "feedback": "Correct! When e=3 and the message is small enough that m^3 < n, the modular reduction doesn't occur, allowing direct calculation of the cube root to recover the plaintext."}, {"id": "rsa_opt3", "text": "Fermat Factorization - Factor n using <PERSON><PERSON><PERSON>'s method since n is likely a product of two primes that are close together", "is_correct": false, "feedback": "While factoring n would break the RSA encryption, there's a more direct attack given the low exponent and likely small message size."}, {"id": "rsa_opt4", "text": "Timing Attack - Analyze the time taken for decryption operations to recover the private key", "is_correct": false, "feedback": "A timing attack requires access to the decryption process and timing measurements, which aren't available in this scenario."}], "feedback_correct": "Excellent! You correctly identified the vulnerability created by using a low public exponent (e=3) without proper padding, which allows direct recovery of short messages.", "feedback_incorrect": "This scenario describes a textbook RSA implementation with a very low public exponent (e=3) and no padding. When encrypting small messages, m^3 may be less than n, meaning no modular reduction occurs during encryption.", "explanation": "This scenario demonstrates a classic RSA implementation vulnerability when using a low public exponent (e=3) without proper padding. In RSA, encryption is computed as c = m^e mod n. However, if m is small enough that m^e < n, the modular reduction doesn't actually occur, and c = m^e exactly. In this case, with e=3, you can simply compute the cube root of the ciphertext to recover the original message: m = ∛c. This attack works because without proper padding (like PKCS#1 OAEP), the message space isn't expanded to ensure that m^e > n. This vulnerability highlights why real-world RSA implementations should: 1) Use a larger public exponent (typically 65537), 2) Always implement proper padding schemes, and 3) Ensure messages are properly padded to an appropriate length. Modern standards like PKCS#1 v2.0 with OAEP padding address these concerns by randomizing the message and expanding it to the full size of the modulus."}, {"question_id": "side_channel_timing", "type": "multiple_choice", "difficulty": "advanced", "text": "You're analyzing a web application that verifies API keys using the following Node.js code:\n\n```javascript\nfunction verifyApiKey(providedKey, actualKey) {\n  if (providedKey.length !== actualKey.length) {\n    return false;\n  }\n  \n  for (let i = 0; i < actualKey.length; i++) {\n    if (providedKey.charAt(i) !== actualKey.charAt(i)) {\n      return false;\n    }\n  }\n  \n  return true;\n}\n```\n\nWhat vulnerability exists in this implementation, and how would you exploit it?", "points": 2, "single_correct_answer": true, "options": [{"id": "timing_opt1", "text": "Buffer Overflow - Send an extremely long API key to crash the application", "is_correct": false, "feedback": "JavaScript doesn't suffer from traditional buffer overflows, and the length check would prevent this attack anyway."}, {"id": "timing_opt2", "text": "Timing Attack - Iteratively guess each character of the API key by measuring response times", "is_correct": true, "feedback": "Correct! This implementation compares characters one-by-one and returns immediately on the first mismatch, creating timing differences that reveal how many characters were correct."}, {"id": "timing_opt3", "text": "Regular Expression DoS - Craft a key that causes catastrophic backtracking", "is_correct": false, "feedback": "This code doesn't use regular expressions, so a ReDoS attack isn't applicable."}, {"id": "timing_opt4", "text": "Type Confusion - Send non-string values to cause unexpected behavior", "is_correct": false, "feedback": "While JavaScript type coercion can lead to vulnerabilities, the key issue here is the string comparison method, not type handling."}], "feedback_correct": "Excellent! You correctly identified the timing side-channel vulnerability, where the early-exit string comparison leaks information about how many characters were correct.", "feedback_incorrect": "The code is vulnerable to a timing attack. Since it compares characters sequentially and returns as soon as a mismatch is found, the verification time varies based on how many characters are correct.", "explanation": "This code contains a classic timing side-channel vulnerability in string comparison. Since the function returns immediately when it finds a mismatched character, the time it takes to execute depends on how many characters match before the first difference. An attacker can exploit this by systematically guessing each character of the API key and measuring the response time. A slightly longer response time indicates that the guessed character is correct, as the function proceeded to check the next character. By repeating this process for each position, the attacker can determine the entire API key character by character. To prevent this vulnerability, applications should use constant-time comparison functions that always take the same amount of time regardless of how many characters match. In Node.js, this could be implemented using the `crypto.timingSafeEqual()` function, which is specifically designed to resist timing attacks. This example demonstrates why security-sensitive comparisons (like API keys, passwords, or HMAC verification) should never use standard string comparison methods."}]}}