{
  "quiz": {
    "$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    "metadata": {
      "format_version": "1.1",
      "quiz_id": "sql-injection-prevention",
      "title": "SQL Injection Prevention Challenge",
      "description": "Test your ability to identify and prevent SQL injection vulnerabilities with code examples.",
      "author": "Cascade AI Assistant",
      "creation_date": "2025-05-25T12:37:17Z",
      "tags": ["sql-injection", "sqli", "secure-coding", "web-security", "php"],
      "passing_score_percentage": 70,
      "time_limit_minutes": 20,
      "markup_format": "markdown",
      "locale": "en-US"
    },
    "questions": [
      {
        "question_id": "sqli_q1_identify_vulnerable",
        "type": "multiple_choice",
        "text": "Which of the following PHP code snippets is vulnerable to SQL Injection?\n\n**Snippet A:**\n```php\n$id = $_GET['id'];\n$stmt = $pdo->prepare('SELECT * FROM products WHERE id = :id');\n$stmt->bindParam(':id', $id);\n$stmt->execute();\n$product = $stmt->fetch();\n```\n\n**Snippet B:**\n```php\n$id = $_GET['id'];\n$query = \"SELECT * FROM products WHERE id = '" . $id . \"'\";\n$result = $mysqli->query($query);\n$product = $result->fetch_assoc();\n```\n\n**Snippet C:**\n```php\n$id = (int)$_GET['id'];\n$query = \"SELECT * FROM products WHERE id = " . $id;\n$result = $mysqli->query($query);\n$product = $result->fetch_assoc();\n```",
        "points": 1,
        "single_correct_answer": true,
        "options": [
          {
            "id": "q1_opt1",
            "text": "Snippet A",
            "is_correct": false
          },
          {
            "id": "q1_opt2",
            "text": "Snippet B",
            "is_correct": true,
            "feedback": "Correct! Snippet B directly concatenates user input into the SQL query."
          },
          {        "question_id": "sqli_q1_identify_vulnerable",
                  "type": "multiple_choice",
                  "difficulty": "beginner", {
                    "question_id": "mobile_sec_deep_link",
                    "type": "multiple_choice",
                    "difficulty": "advanced",
                    "text": "You're conducting a security assessment of an Android banking application. In the AndroidManifest.xml file, you discover the following intent filter:\n\n```xml\n<activity android:name=\".TransferActivity\">\n  <intent-filter>\n    <action android:name=\"android.intent.action.VIEW\" />\n    <category android:name=\"android.intent.category.DEFAULT\" />\n    <category android:name=\"android.intent.category.BROWSABLE\" />\n    <data android:scheme=\"bankapp\" android:host=\"transfer\" />\n  </intent-filter>\n</activity>\n```\n\nYou also notice the application doesn't validate the origin of deep link invocations. Which of the following attack vectors could potentially exploit this configuration?",
                    "points": 2,
                    "single_correct_answer": true,
                    "options": [
                      {
                        "id": "dl_opt1",
                        "text": "Creating a malicious application that launches the deep link `bankapp://transfer?account=attacker&amount=1000` to initiate unauthorized transfers",
                        "is_correct": true,
                        "feedback": "Correct! Without proper origin validation, a malicious app can invoke the deep link with parameters that might initiate a transfer if the victim is already authenticated."
                      },
                      {
                        "id": "dl_opt2",
                        "text": "Using an XSS vulnerability in a web view to directly access the local file system",
                        "is_correct": false,
                        "feedback": "While XSS in WebViews is dangerous, this scenario specifically relates to deep link hijacking, not WebView exploitation."
                      },
                      {
                        "id": "dl_opt3",
                        "text": "Bypassing the Android permission system to access sensitive device APIs",
                        "is_correct": false,
                        "feedback": "Deep link vulnerabilities don't directly bypass the Android permission system, which is a separate security mechanism."
                      },
                      {
                        "id": "dl_opt4",
                        "text": "Intercepting TLS traffic between the app and the banking server",
                        "is_correct": false,
                        "feedback": "This describes a Man-in-the-Middle attack, not a deep link exploitation, which involves invoking app components through URIs."
                      }
                    ],
                    "feedback_correct": "Excellent! When deep links aren't properly validated and handle sensitive operations, malicious apps can invoke them with crafted parameters to perform unauthorized actions.",
                    "feedback_incorrect": "The primary security concern with this manifest configuration is that the deep link (bankapp://transfer) can be invoked by any app or website without proper validation, potentially leading to unauthorized transfers if parameters are accepted from the link.",
                    "explanation": "Android deep links allow apps to be launched with specific URIs (e.g., bankapp://transfer). When an app defines a deep link in AndroidManifest.xml with categories DEFAULT and BROWSABLE, it can be invoked from both other apps and web browsers. If the app doesn't validate the origin of these invocations and performs sensitive operations based on parameters from the URI (like transferring money), it creates a serious security vulnerability. An attacker could create a malicious app or website that launches the deep link with crafted parameters to trick users into performing unauthorized actions. This is especially dangerous if the user is already authenticated in the target app and the app doesn't require re-authentication for sensitive operations."
                  }
            "id": "q1_opt3",
            "text": "Snippet C",
            "is_correct": false
          }
        ],
        "feedback_correct": "Excellent! Snippet B is vulnerable due to direct string concatenation of user input into the SQL query.",
        "feedback_incorrect": "Incorrect. Snippet B is vulnerable. Snippet A uses prepared statements (a strong defense), and Snippet C casts the input to an integer (also a good defense for numeric input), mitigating the risk.",
        "explanation": "SQL Injection occurs when untrusted data (like `$_GET['id']`) is sent to an SQL interpreter as part of a command or query without proper sanitization or parameterization. Snippet B is vulnerable because it directly includes the raw `$id` variable in the SQL string. An attacker could provide input like `1' OR '1'='1` to manipulate the query. Snippet A uses prepared statements, which separate the SQL logic from the data. Snippet C type-casts the input to an integer, which is effective if the ID is expected to be numeric.",
      },
      {
        "question_id": "sqli_q2_fix_vulnerable",
        "type": "multiple_choice",
        "difficulty": "beginner",
        "text": "Consider the following vulnerable PHP code:\n```php\n$username = $_POST['username'];\n$password = $_POST['password'];\n$query = \"SELECT * FROM users WHERE username = '" . $username . "' AND password = '" . $password . "'\";\n// ... execute query ...\n```\nWhich of the following is the **most effective** way to prevent SQL injection in this scenario?",
        "points": 1,
        "single_correct_answer": true,
        "options": [
          {
            "id": "q2_opt1",
            "text": "Using `addslashes()` on `$username` and `$password`.",
            "is_correct": false,
            "feedback": "While `addslashes()` might seem helpful, it's not a foolproof solution and can be bypassed in some contexts or with certain database configurations."
          },
          {
            "id": "q2_opt2",
            "text": "Implementing a Web Application Firewall (WAF).",
            "is_correct": false,
            "feedback": "A WAF is a valuable defense-in-depth measure, but it shouldn't be the primary or sole defense against SQLi. Secure coding practices are essential."
          },
          {
            "id": "q2_opt3",
            "text": "Using prepared statements with parameterized queries.",
            "is_correct": true,
            "feedback": "Correct! Prepared statements ensure that user input is treated as data, not executable code."
          },
          {
            "id": "q2_opt4",
            "text": "Encoding output to HTML entities.",
            "is_correct": false,
            "feedback": "Encoding output is crucial for preventing XSS, but it does not prevent SQL injection, which is an input validation and database interaction issue."
          }
        ],
        "feedback_correct": "Precisely! Prepared statements with parameterized queries are the gold standard for preventing SQL injection.",
        "feedback_incorrect": "The most robust solution is using prepared statements. Other methods might offer some protection but are generally less effective or serve different purposes.",
        "explanation": "Prepared statements (or parameterized queries) are the most effective way to prevent SQL injection. They separate the SQL command structure from the data. The database compiles the SQL command template first, and then the user-supplied data is sent later, treated strictly as data, not as part of the executable SQL command. This prevents malicious input from altering the query's logic."
      }
    ]
  }
}
